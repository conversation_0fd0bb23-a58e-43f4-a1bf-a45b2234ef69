<template>
  <div class="flex h-full flex-col">
    <el-scrollbar height="h-0 flex-1">
      <ul v-loading="fieldLoading" class="p-5">
        <li class="demand-item">
          <div class="demand-row">
            <div>项目数据集管理</div>
            <div v-if="!readonly" class="flex gap-2">
              <el-button type="primary" :loading="addLoading" @click="onAddDataset"> 添加数据集 </el-button>
              <el-button
                :disabled="selectedDatasets.length <= 0"
                type="danger"
                :loading="removeLoading"
                @click="onRemoveDatasets"
              >
                移除选中 ({{ selectedDatasets.length }})
              </el-button>
            </div>
          </div>
          <div class="demand-content">
            <!-- 数据集列表表格 -->
            <el-table
              ref="tableRef"
              :data="datasetList"
              style="width: 100%"
              class="c-table c-table-header"
              :row-key="(row) => row.id"
              @selection-change="handleSelectionChange"
            >
              <el-table-column v-if="!readonly" type="selection" width="55" />
              <el-table-column type="index" label="序号" width="80" />
              <el-table-column prop="datasetNameCn" label="数据集名称(中文)" show-overflow-tooltip />
              <el-table-column prop="datasetName" label="数据集名称(英文)" show-overflow-tooltip />
              <el-table-column prop="projectCode" label="课题编码缩写" />
              <el-table-column prop="diseaseType" label="疾病类型" />
              <el-table-column prop="createDate" label="创建日期" />
              <el-table-column prop="projectLeader" label="项目负责人" />
              <el-table-column prop="affiliatedUnit" label="所属单位" show-overflow-tooltip />
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button link type="primary" @click="onViewDatasetDetail(row)">查看详情</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div v-if="total > 0" class="pagination-container mt-4 flex justify-center">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>

            <!-- 空状态 -->
            <div v-if="!tableLoading && datasetList.length === 0" class="empty-state">
              <el-empty description="暂无数据集" />
            </div>
          </div>
        </li>

        <li class="demand-item">
          <div class="demand-row">
            <div>延长研究时限</div>
          </div>
          <div class="demand-content">
            <el-form ref="limitRef" :model="limitForm" :rules="limitRules" label-position="top">
              <div class="flex">
                <el-form-item label="当前时限：">
                  <el-input v-model="limitForm.currentValue" disabled style="width: 200px" />
                  <span class="ml-2">个月</span>
                </el-form-item>
              </div>
              <div v-if="!readonly" class="flex">
                <el-form-item label="延长时限：">
                  <el-input-number
                    v-model="limitForm.newValue"
                    :min="1"
                    :max="999"
                    step-strictly
                    style="width: 200px"
                  />
                  <span class="ml-2">个月</span>
                </el-form-item>
              </div>

              <el-form-item v-if="!readonly">
                <el-button :loading="saveLoading" type="primary" @click="onChangeDate">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </li>
      </ul>
    </el-scrollbar>

    <!-- 数据集选择对话框 -->
    <ApplicationDatasetDialog
      v-model:visible="showDatasetDialog"
      :application-id="props.id"
      @success="onDatasetAdded"
    />
  </div>
</template>

<script setup lang="ts">
  /* 更新需求 */
  import { findFileInforByApplicationId, findAppById, newOrUpdateApplication_02, removeInfor } from '@/api/index';
  import { useUsers } from '@/store/user-info.js';
  import { ElMessage, ElMessageBox, ElTable } from 'element-plus';
  import { useRouter } from 'vue-router';
  import ApplicationDatasetDialog from './ApplicationDatasetDialog.vue';

  const store = useUsers();
  const router = useRouter();
  const props = defineProps({
    readonly: { type: Boolean, default: false },
    id: String,
  });

  // 数据集相关的响应式数据
  const fieldLoading = ref(false);
  const tableLoading = ref(false);
  const addLoading = ref(false);
  const removeLoading = ref(false);
  const datasetList = ref<FileInfoVO[]>([]);
  const selectedDatasets = ref<FileInfoVO[]>([]);
  const showDatasetDialog = ref(false);
  const tableRef = ref<InstanceType<typeof ElTable>>();

  // 分页数据
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 获取项目申请中的数据集列表
  async function fetchDatasetList(pageNum = 1) {
    try {
      tableLoading.value = true;
      const { data } = await findFileInforByApplicationId(+props.id!, pageNum, pagination.pageSize);
      datasetList.value = data?.content || [];
      total.value = data?.totalElement || 0;
      pagination.page = pageNum;
    } catch (error) {
      console.log('🚀 ~ fetchDatasetList ~ error:', error);
      ElMessage.error('获取数据集列表失败');
    } finally {
      tableLoading.value = false;
    }
  }

  // 表格选择变化处理
  const handleSelectionChange = (selection: FileInfoVO[]) => {
    selectedDatasets.value = selection;
  };

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    fetchDatasetList(1);
  };

  const handleCurrentChange = (page: number) => {
    fetchDatasetList(page);
  };

  // 添加数据集
  const onAddDataset = () => {
    showDatasetDialog.value = true;
  };

  // 数据集添加成功回调
  const onDatasetAdded = () => {
    fetchDatasetList(pagination.page);
  };

  // 查看数据集详情
  const onViewDatasetDetail = (dataset: FileInfoVO) => {
    router.push({ name: 'PersonalProjectOrderSelect', query: { orderId: dataset.id } });
  };

  // 移除选中的数据集
  const onRemoveDatasets = async () => {
    if (selectedDatasets.value.length === 0) {
      ElMessage.warning('请选择要移除的数据集');
      return;
    }

    try {
      await ElMessageBox.confirm(`确定要移除选中的 ${selectedDatasets.value.length} 个数据集吗？`, '确认移除', {
        type: 'warning',
      });

      removeLoading.value = true;
      const datasetIds = selectedDatasets.value.map((item) => item.id!);

      await removeInfor(datasetIds, { applicationId: +props.id! });

      ElMessage.success('数据集移除成功');
      selectedDatasets.value = [];
      tableRef.value?.clearSelection();
      fetchDatasetList(pagination.page);
    } catch (error) {
      if (error !== 'cancel') {
        console.error('移除数据集失败:', error);
        ElMessage.error('移除数据集失败');
      }
    } finally {
      removeLoading.value = false;
    }
  };

  // 延长研究时限相关
  const limitRef = ref();
  const limitForm = reactive({
    currentValue: 0,
    newValue: 1,
  });
  const limitRules = reactive({
    newValue: [{ required: true, message: '请输入延长时限', trigger: 'blur' }],
  });
  const saveLoading = ref(false);
  const appData = ref<ApplicationVO | null>(null);

  async function fetchDate() {
    try {
      fieldLoading.value = true;
      const { data } = await findAppById(+props.id!);
      appData.value = data || null;
      limitForm.currentValue = data?.duration || 0;
    } catch (error) {
      console.log(error);
    } finally {
      fieldLoading.value = false;
    }
  }

  const onChangeDate = () => {
    limitRef.value.validate(async (valid: boolean) => {
      if (valid) {
        try {
          saveLoading.value = true;
          appData.value!.duration = limitForm.currentValue + limitForm.newValue;
          await newOrUpdateApplication_02(store.user.id, appData.value!);
          fetchDate();
          ElMessage({
            type: 'success',
            message: '修改成功',
          });
        } catch (error) {
          console.log(error);
        } finally {
          saveLoading.value = false;
        }
      }
    });
  };

  onBeforeMount(() => {
    fetchDatasetList();
    fetchDate();
  });
</script>

<style lang="scss" scoped>
  .demand-item {
    border-radius: 4px;
    background: #fff;
    margin-bottom: 20px;

    .demand-row {
      padding-left: 28px;
      padding-right: 40px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .demand-content {
      border-top: 1px solid #e1e3e6;
      padding: 20px 40px;
    }
  }

  .pagination-container {
    margin-top: 16px;
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }

  .c-table {
    --el-border-color-lighter: #ebeef5;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }

  .footer {
    display: flex;
    justify-content: center;
    background: #fff;
    padding: 16px 0;
    box-shadow: var(--el-box-shadow-light);
    z-index: 1;
  }
</style>
